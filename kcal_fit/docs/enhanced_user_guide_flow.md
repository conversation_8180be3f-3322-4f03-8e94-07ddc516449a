# 优化的统一用户引导流程

## 概述

优化了首次打开应用的用户引导流程，现在使用**单个引导弹窗**包含两个步骤：
1. **第一步：身体数据录入** - 录入基本身体信息
2. **第二步：目标设置录入** - 设定健康目标

## 设计亮点

- ✨ **统一弹窗设计**：一个弹窗完成所有引导，避免多次弹窗打扰
- 🎯 **步骤指示器**：清晰显示当前进度和步骤
- 🔄 **流畅切换**：步骤间无缝切换，体验更佳
- 📱 **响应式布局**：适配不同屏幕尺寸

## 流程设计

### 引导触发条件

1. **身体数据引导**：
   - 检查 `body_data` 表中是否有记录
   - 如果没有身体数据，显示身体数据录入引导

2. **目标设置引导**：
   - 检查 Hive 中 `goals` 存储是否有目标数据
   - 检查字段：`goalTypes`、`targetWeight`、`activityLevel`
   - 如果没有目标数据，显示目标设置引导

### 引导流程

```
应用启动
    ↓
检查身体数据
    ↓
[无身体数据] → 显示身体数据引导 → 用户完成录入 → 检查目标设置 → [无目标] → 显示目标设置引导
    ↓                                                      ↓
[有身体数据] → 检查目标设置 → [无目标] → 显示目标设置引导    [有目标] → 完成引导
    ↓                           ↓
[有目标] → 完成引导              完成引导
```

## 技术实现

### 新增状态管理

```dart
/// 是否已显示目标设置引导
final RxBool goalSettingGuideShown = false.obs;

/// 存储键名
static const String _goalSettingGuideShownKey = 'goal_setting_guide_shown';
```

### 核心方法

1. **`_buildUnifiedGuideDialog()`** - 构建统一的引导对话框
2. **`_buildStepIndicator()`** - 构建步骤指示器组件
3. **`_buildStep1Content()`** - 构建第一步内容（身体数据）
4. **`_buildStep2Content()`** - 构建第二步内容（目标设置）
5. **`_saveAllUserData()`** - 保存所有用户数据
6. **`_shouldShowGoalSettingGuide()`** - 检查是否需要显示目标设置引导
7. **`_markGoalSettingGuideShown()`** - 标记目标设置引导已显示

### 统一引导对话框设计

#### 对话框结构
- **标题区域**：欢迎加入KcalFit + 步骤指示器
- **内容区域**：根据当前步骤动态切换内容
- **按钮区域**：智能显示上一步/下一步/完成按钮

#### 步骤指示器
```
[1] 身体数据 ——————— [2] 健康目标
```
- 圆形数字指示器，激活状态为蓝色
- 连接线显示进度
- 步骤标题显示当前阶段

#### 第一步：身体数据录入
- 标题：第一步：基本信息
- 内容：性别、出生日期、身高、体重
- 按钮：下一步

#### 第二步：目标设置录入
- 标题：第二步：健康目标
- 内容：
  - 健康目标：减脂、增肌、塑型、养生（多选）
  - 目标体重：使用尺子选择器，范围30-150kg
  - 活动水平：久坐不动、轻度活动、中度活动、高强度活动
- 按钮：上一步 + 完成

## 数据存储

### 身体数据存储
- 存储位置：SQLite `body_data` 表
- 字段：`gender`、`birth_date`、`height`、`weight`

### 目标设置存储
- 存储位置：Hive `goals` 存储
- 字段：
  - `goalTypes`：健康目标（逗号分隔的字符串）
  - `targetWeight`：目标体重
  - `activityLevel`：活动水平
  - `createdTime`：创建时间

### 引导状态存储
- 存储位置：Hive `user_guide` 存储
- 字段：
  - `body_data_guide_shown`：身体数据引导已显示
  - `goal_setting_guide_shown`：目标设置引导已显示

## 用户体验优化

1. **统一体验**：单个弹窗完成所有设置，避免多次弹窗干扰
2. **清晰导航**：步骤指示器让用户了解当前进度和剩余步骤
3. **灵活操作**：支持上一步/下一步操作，用户可自由切换
4. **数据预填充**：如果用户已有部分数据，会自动预填充到表单中
5. **防重复显示**：使用状态标记防止重复显示引导
6. **响应式设计**：对话框尺寸优化，适配不同屏幕
7. **优雅的UI**：统一的设计语言，良好的视觉体验

## 测试和重置

### 重置引导状态
```dart
await userGuideService.resetGuideStatus();
```

这将重置所有引导状态，下次启动时重新显示引导。

## 扩展性

该设计支持未来添加更多引导步骤：
1. 在 `UserGuideService` 中添加新的状态变量
2. 实现对应的检查和显示逻辑
3. 在引导流程中添加新的步骤

## 注意事项

1. **性能考虑**：引导检查在UI加载完成后延迟执行
2. **错误处理**：所有引导相关操作都有异常处理
3. **用户体验**：提供跳过选项，避免强制性引导
4. **数据安全**：引导状态仅存储在本地
