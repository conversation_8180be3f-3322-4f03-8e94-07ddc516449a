import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:health/health.dart';

import 'app/modules/index/controllers/index_controller.dart';
import 'app/routes/app_pages.dart';
import 'core/constants/locales.g.dart';
import 'core/init/app_initializer.dart';
import 'core/services/theme_service.dart';

/// 应用程序主入口
/// 负责初始化应用并启动GetMaterialApp
void main() async {
  await AppInitializer.initialize();
  runApp(const KcalFitApp());
}

/// KcalFit应用程序主类
/// 配置应用的基本设置、主题、路由和本地化
class KcalFitApp extends StatelessWidget {
  const KcalFitApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeService = Get.find<ThemeService>();

    return GetMaterialApp(
      // 应用基本信息
      title: "KcalFit - 卡路里记录",

      // 本地化配置
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [Locale('zh', 'CN'), Locale('en', 'US')],
      translationsKeys: AppTranslation.translations,
      locale: const Locale('zh', 'CN'),
      fallbackLocale: const Locale('en', 'US'),

      // 路由配置
      initialRoute: AppPages.initial,
      getPages: AppPages.routes,
      routingCallback: _handleRoutingChange,

      // UI配置 - 使用主题服务提供的主题
      theme: themeService.getCurrentTheme(),
      defaultTransition: Transition.cupertino,
      debugShowCheckedModeBanner: false,

      // GetX配置
      enableLog: true,
      popGesture: true,
    );
  }
}

/// 处理路由变化的回调函数
/// 在特定路由变化时执行相应的业务逻辑
void _handleRoutingChange(Routing? routing) {
  if (routing?.current == Routes.HOME) {
    // 返回首页时刷新数据
    _refreshHomeData();
  }
}

/// 刷新首页数据
/// 当用户返回首页时，刷新相关数据以确保数据的实时性
void _refreshHomeData() {
  try {
    // 检查IndexController是否已注册
    if (Get.isRegistered<IndexController>()) {
      Get.find<IndexController>().refreshTodayData();
    }
  } catch (e) {
    // 如果控制器未注册，忽略错误
    // 这种情况通常发生在应用首次启动时
  }
}
