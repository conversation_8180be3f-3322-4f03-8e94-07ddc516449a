import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scroll_datetime_picker/scroll_datetime_picker.dart';
import 'package:intl/intl.dart';
import 'package:flutter_ruler_picker/flutter_ruler_picker.dart';

import '../utils/logger_util.dart';

/// 用户数据输入组件集合
/// 包含性别、年龄、身高、体重选择组件
class UserDataInputWidgets {
  /// 构建性别选择组件
  /// 提供女性和男性两个选项，支持点击切换
  /// [selectedGender] 当前选中的性别值
  static Widget buildGenderSelection(RxString selectedGender) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('性别', style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: Colors.black87)),
        const SizedBox(height: 8),
        Obx(
          () => Row(
            children: [
              // 女性选择
              Expanded(
                child: _buildGenderOption(
                  gender: '女',
                  isSelected: selectedGender.value == '女',
                  onTap: () => selectedGender.value = '女',
                  selectedColor: const Color(0xFFFF6B9D),
                  backgroundColor: const Color(0xFFFFF0F5),
                  borderColor: const Color(0xFFFFB6C1),
                  icon: Icons.female,
                ),
              ),
              const SizedBox(width: 12),
              // 男性选择
              Expanded(
                child: _buildGenderOption(
                  gender: '男',
                  isSelected: selectedGender.value == '男',
                  onTap: () => selectedGender.value = '男',
                  selectedColor: const Color(0xFF4A90E2),
                  backgroundColor: const Color(0xFFF0F8FF),
                  borderColor: const Color(0xFF87CEEB),
                  icon: Icons.male,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建单个性别选项组件
  /// [gender] 性别文本
  /// [isSelected] 是否被选中
  /// [onTap] 点击回调
  /// [selectedColor] 选中时的主色调
  /// [backgroundColor] 背景色
  /// [borderColor] 边框色
  /// [icon] 图标
  static Widget _buildGenderOption({
    required String gender,
    required bool isSelected,
    required VoidCallback onTap,
    required Color selectedColor,
    required Color backgroundColor,
    required Color borderColor,
    required IconData icon,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200), // 动画过渡时间
      curve: Curves.easeInOut, // 动画过渡曲线
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8), // 调整内边距
          decoration: BoxDecoration(
            color: isSelected ? backgroundColor : Colors.grey.shade50,
            borderRadius: BorderRadius.circular(10), // 稍微减小圆角
            border: Border.all(color: isSelected ? selectedColor : Colors.grey.shade200, width: isSelected ? 2 : 1),
            boxShadow:
                isSelected
                    ? [
                      BoxShadow(
                        color: selectedColor.withValues(alpha: 0.15),
                        blurRadius: 4, // 减小阴影模糊度
                        offset: const Offset(0, 1), // 减小阴影偏移
                      ),
                    ]
                    : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 头像或图标
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 32, // 从44减小到32
                    height: 32, // 从44减小到32
                    decoration: BoxDecoration(
                      color: isSelected ? selectedColor.withValues(alpha: 0.1) : Colors.grey.shade100,
                      shape: BoxShape.circle,
                      border: Border.all(color: isSelected ? selectedColor : Colors.grey.shade300, width: 1),
                    ),
                    child: Icon(
                      icon,
                      size: 18,
                      color: isSelected ? selectedColor : Colors.grey.shade600,
                    ), // 图标尺寸从22减小到18
                  ),
                  // 选中状态的勾选标记
                  if (isSelected)
                    Positioned(
                      bottom: -2,
                      right: -2,
                      child: Container(
                        width: 14, // 从16减小到14
                        height: 14, // 从16减小到14
                        decoration: BoxDecoration(
                          color: selectedColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 1),
                        ),
                        child: const Icon(Icons.check, size: 8, color: Colors.white), // 勾选图标从10减小到8
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 6), // 从8减小到6
              // 性别文本
              Text(
                gender,
                style: TextStyle(
                  fontSize: 12, // 从13减小到12
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color: isSelected ? selectedColor : Colors.grey.shade700,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建年龄选择组件
  /// 使用ScrollDateTimePicker实现出生日期选择
  /// [selectedBirthDate] 当前选中的出生日期（可为null）
  static Widget buildAgeSelection(Rxn<DateTime> selectedBirthDate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('年龄', style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: Colors.black87)),
        // 使用Obx监听日期变化，只重建显示文本部分
        // Obx(() {
        //   final date = selectedBirthDate.value;
        //   return Text(
        //     date != null ? '${date.year}年${date.month}月${date.day}日' : '请选择出生日期',
        //     style: TextStyle(fontSize: 13, color: date != null ? Colors.grey : Colors.grey.shade400),
        //   );
        // }),
        // const SizedBox(height: 8),
        Container(
          height: 65,
          decoration: const BoxDecoration(),
          child: Obx(() {
            final date = selectedBirthDate.value;
            // 只有当日期不为null时才显示日期选择器
            if (date != null) {
              return _DatePickerWidget(selectedBirthDate: selectedBirthDate);
            } else {
              // 显示加载中占位符
              return const Center(child: Text('正在加载...', style: TextStyle(fontSize: 14, color: Colors.grey)));
            }
          }),
        ),
      ],
    );
  }

  /// 构建身高选择组件
  /// 使用flutter_ruler_picker实现身高选择
  /// [selectedHeight] 当前选中的身高值
  /// [heightController] 尺子控制器
  static Widget buildHeightSelection(RxInt selectedHeight, RulerPickerController heightController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('身高', style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: Colors.black87)),
        const SizedBox(height: 8),
        // 显示当前选中的身高值
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 6),
          child: Obx(
            () => Text(
              '${selectedHeight.value} cm',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 20, color: Colors.black, fontWeight: FontWeight.w500),
            ),
          ),
        ),
        const SizedBox(height: 6),
        // 身高选择尺子
        SizedBox(
          height: 70,
          child: RulerPicker(
            controller: heightController,
            onBuildRulerScaleText: (index, value) {
              return value.toInt().toString();
            },
            ranges: const [
              RulerRange(begin: 140, end: 220, scale: 1), // 身高范围140-220cm，刻度为1cm
            ],
            scaleLineStyleList: const [
              ScaleLineStyle(
                color: Colors.grey,
                width: 1.5,
                height: 30,
                scale: 0, // 主刻度线（每10cm）
              ),
              ScaleLineStyle(
                color: Colors.grey,
                width: 1,
                height: 25,
                scale: 5, // 中刻度线（每5cm）
              ),
              ScaleLineStyle(
                color: Colors.grey,
                width: 1,
                height: 15,
                scale: -1, // 小刻度线（每1cm）
              ),
            ],
            onValueChanged: (value) {
              selectedHeight.value = value.round();
            },
            width: Get.width * 0.75,
            height: 70,
            rulerMarginTop: 6,
            // 自定义标记线
            marker: Container(
              width: 3,
              height: 45,
              decoration: BoxDecoration(color: const Color(0xFF4A90E2), borderRadius: BorderRadius.circular(2)),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建体重选择组件
  /// 使用flutter_ruler_picker实现体重选择，支持0.1kg精度
  /// [selectedWeight] 当前选中的体重值
  /// [weightController] 尺子控制器
  static Widget buildWeightSelection(RxDouble selectedWeight, RulerPickerController weightController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('体重', style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: Colors.black87)),
        const SizedBox(height: 8),
        // 显示当前选中的体重值
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 6),
          child: Obx(
            () => Text(
              '${selectedWeight.value.toStringAsFixed(1)} kg',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 20, color: Colors.black, fontWeight: FontWeight.w500),
            ),
          ),
        ),
        const SizedBox(height: 6),
        // 体重选择尺子
        SizedBox(
          height: 70,
          child: RulerPicker(
            controller: weightController,
            onBuildRulerScaleText: (index, value) {
              // 只在整数位置显示文本，避免过于密集
              if (value % 5 == 0) {
                return value.toInt().toString();
              }
              return '';
            },
            ranges: const [
              RulerRange(begin: 30, end: 150, scale: 0.1), // 体重范围30-150kg，刻度为0.1kg
            ],
            scaleLineStyleList: const [
              ScaleLineStyle(
                color: Colors.grey,
                width: 1.5,
                height: 30,
                scale: 0, // 主刻度线（每5kg）
              ),
              ScaleLineStyle(
                color: Colors.grey,
                width: 1,
                height: 25,
                scale: 10, // 中刻度线（每1kg）
              ),
              ScaleLineStyle(
                color: Colors.grey,
                width: 1,
                height: 15,
                scale: -1, // 小刻度线（每0.1kg）
              ),
            ],
            onValueChanged: (value) {
              // 保留一位小数精度
              selectedWeight.value = double.parse(value.toStringAsFixed(1));
            },
            width: Get.width * 0.75,
            height: 70,
            rulerMarginTop: 6,
            // 自定义标记线
            marker: Container(
              width: 3,
              height: 45,
              decoration: BoxDecoration(color: const Color(0xFF4A90E2), borderRadius: BorderRadius.circular(2)),
            ),
          ),
        ),
      ],
    );
  }
}

/// 日期选择器组件
/// 用于解决ScrollDateTimePicker初始化时触发onChange的问题
class _DatePickerWidget extends StatefulWidget {
  final Rxn<DateTime> selectedBirthDate;

  const _DatePickerWidget({required this.selectedBirthDate});

  @override
  State<_DatePickerWidget> createState() => _DatePickerWidgetState();
}

class _DatePickerWidgetState extends State<_DatePickerWidget> {
  late DateTime _lastSetDate;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _lastSetDate = widget.selectedBirthDate.value ?? DateTime(1990, 1, 1);
    LoggerUtil.d('初始化日期选择器，初始日期: $_lastSetDate');

    // 延迟标记为已初始化，避免初始化时的onChange调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _isInitialized = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final currentDate = widget.selectedBirthDate.value ?? DateTime(1990, 1, 1);

      // 如果日期发生了外部更新（比如从数据库加载），更新内部状态
      if (_isInitialized && !currentDate.isAtSameMomentAs(_lastSetDate)) {
        _lastSetDate = currentDate;
        LoggerUtil.d('外部日期更新，同步内部状态: $currentDate');
      }

      return ScrollDateTimePicker(
        itemExtent: 22, // 每个选项的高度
        infiniteScroll: false, // 无限滚动
        dateOption: DateTimePickerOption(
          dateFormat: DateFormat('yyyy年MM月dd日'),
          minDate: DateTime(1900, 1, 1),
          maxDate: DateTime.now(),
          initialDate: currentDate, // 直接使用当前日期值
        ),
        wheelOption: const DateTimePickerWheelOption(
          perspective: 0.005, // 减少透视效果，使滚轮更平
          diameterRatio: 2.0, // 增加直径比例，减少弯曲
          squeeze: 1.0, // 保持正常挤压
          offAxisFraction: 0.0, // 保持在轴上
          physics: ClampingScrollPhysics(), // 使用ClampingScrollPhysics减少惯性滚动
        ),
        style: DateTimePickerStyle(
          activeStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: Colors.black87), // 选中的日期样式
          inactiveStyle: const TextStyle(fontSize: 16, color: Colors.grey), // 未选中的日期样式
        ),
        onChange: (datetime) {
          LoggerUtil.d('onChange被调用，新日期: $datetime，上次设置的日期: $_lastSetDate，已初始化: $_isInitialized');

          // 只有在组件完全初始化后才处理onChange
          if (!_isInitialized) {
            LoggerUtil.d('组件未完全初始化，忽略onChange调用');
            return;
          }

          // 检查是否是真正的用户操作（日期确实发生了变化）
          if (datetime.isAtSameMomentAs(_lastSetDate)) {
            LoggerUtil.d('日期未发生变化，忽略onChange调用');
            return;
          }

          LoggerUtil.i('用户选择的日期: $datetime');
          _lastSetDate = datetime;
          widget.selectedBirthDate.value = datetime;
        },
      );
    });
  }
}
