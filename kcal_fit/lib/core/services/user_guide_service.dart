import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_ruler_picker/flutter_ruler_picker.dart';
import '../../app/data/services/hive/hive_service.dart';
import '../../app/data/services/sqflite/dao/impl/body_data_dao.dart';
import '../../app/data/models/body_data.dart';
import '../utils/logger_util.dart';
import '../widgets/user_data_input_widgets.dart';
import '../constants/health_goals.dart';

/// 用户引导服务
/// 负责检测首次使用并引导用户完成必要的设置
class UserGuideService extends GetxService {
  static const String _boxName = 'user_guide';
  static const String _firstLaunchKey = 'first_launch';
  static const String _bodyDataGuideShownKey = 'body_data_guide_shown';
  static const String _goalSettingGuideShownKey = 'goal_setting_guide_shown';

  final BodyDataDao _bodyDataDao = BodyDataDao();

  /// 是否首次启动
  final RxBool isFirstLaunch = true.obs;

  /// 是否已显示身体数据引导
  final RxBool bodyDataGuideShown = false.obs;

  /// 是否已显示目标设置引导
  final RxBool goalSettingGuideShown = false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadGuideStatus();
  }

  /// 加载引导状态
  Future<void> _loadGuideStatus() async {
    try {
      // 检查是否首次启动
      final firstLaunch = await HiveService.getData<bool>(_boxName, _firstLaunchKey);
      isFirstLaunch.value = firstLaunch ?? true;

      // 检查是否已显示身体数据引导
      final guideShown = await HiveService.getData<bool>(_boxName, _bodyDataGuideShownKey);
      bodyDataGuideShown.value = guideShown ?? false;

      // 检查是否已显示目标设置引导
      final goalGuideShown = await HiveService.getData<bool>(_boxName, _goalSettingGuideShownKey);
      goalSettingGuideShown.value = goalGuideShown ?? false;

      LoggerUtil.i(
        '用户引导状态加载完成 - 首次启动: ${isFirstLaunch.value}, 身体数据引导已显示: ${bodyDataGuideShown.value}, 目标设置引导已显示: ${goalSettingGuideShown.value}',
      );
    } catch (e) {
      LoggerUtil.e('加载用户引导状态失败', e);
    }
  }

  /// 是否正在显示引导
  final RxBool _isShowingGuide = false.obs;

  /// 检查并显示用户引导
  /// 在应用启动后调用，检查是否需要显示引导
  Future<void> checkAndShowGuide() async {
    try {
      // 如果正在显示引导，则不重复显示
      if (_isShowingGuide.value) {
        LoggerUtil.i('用户引导正在显示中，跳过重复调用');
        return;
      }

      // 优化：进一步减少延迟时间，避免阻塞UI
      await Future.delayed(const Duration(milliseconds: 100));

      // 异步检查和显示引导，不阻塞主线程
      _checkAndShowGuideAsync();
    } catch (e) {
      LoggerUtil.e('检查用户引导失败', e);
    }
  }

  /// 异步检查和显示引导，避免阻塞主线程
  Future<void> _checkAndShowGuideAsync() async {
    try {
      // 检查是否需要显示身体数据录入引导
      if (await _shouldShowBodyDataGuide()) {
        await _showBodyDataGuide();
        // 注意：身体数据引导完成后的目标设置引导在 _showBodyDataGuide 方法中处理
      } else if (await _shouldShowGoalSettingGuide()) {
        // 如果身体数据已存在，但目标设置不存在，直接显示目标设置引导
        await _showGoalSettingGuide();
      }

      // 标记首次启动完成
      if (isFirstLaunch.value) {
        await _markFirstLaunchComplete();
      }
    } catch (e) {
      LoggerUtil.e('异步检查用户引导失败', e);
    }
  }

  /// 判断是否应该显示身体数据引导
  /// 简化逻辑：无身体数据就显示引导
  Future<bool> _shouldShowBodyDataGuide() async {
    try {
      // 检查是否已有身体数据
      final latestBodyData = await _bodyDataDao.getLatestBodyData();
      final hasBodyData = latestBodyData != null;

      LoggerUtil.i(hasBodyData ? '已有身体数据，不显示引导' : '无身体数据，需要显示引导');
      return !hasBodyData;
    } catch (e) {
      LoggerUtil.e('检查身体数据引导条件失败', e);
      return false;
    }
  }

  /// 显示统一的用户引导
  Future<void> _showBodyDataGuide() async {
    try {
      LoggerUtil.i('显示用户引导');

      // 设置正在显示引导标志
      _isShowingGuide.value = true;

      // 显示统一的引导对话框
      final dialogResult = await Get.dialog<bool>(_buildUnifiedGuideDialog(), barrierDismissible: false);

      // 标记引导已显示
      await _markBodyDataGuideShown();
      await _markGoalSettingGuideShown();

      LoggerUtil.i('用户引导完成，用户选择: ${dialogResult == true ? "确定" : "关闭"}');
    } catch (e) {
      LoggerUtil.e('显示用户引导失败', e);
    } finally {
      // 重置正在显示引导标志
      _isShowingGuide.value = false;
    }
  }

  /// 构建统一的用户引导对话框
  Widget _buildUnifiedGuideDialog() {
    // 当前步骤状态
    final currentStep = 1.obs;

    // 身体数据状态变量
    final selectedGender = '女'.obs;
    final selectedBirthDate = Rxn<DateTime>(DateTime(1990, 7, 15));
    final selectedHeight = 160.obs;
    final selectedWeight = 55.0.obs;

    // 目标设置状态变量
    final selectedGoalTypes = ''.obs;
    final selectedTargetWeight = 60.0.obs;
    final selectedActivityLevel = '轻度活动'.obs;

    // 创建尺子控制器
    final heightController = RulerPickerController(value: selectedHeight.value.toDouble());
    final weightController = RulerPickerController(value: selectedWeight.value);
    final targetWeightController = RulerPickerController(value: selectedTargetWeight.value);

    // 异步加载用户已有数据
    _loadExistingUserData(
      selectedGender,
      selectedBirthDate,
      selectedHeight,
      selectedWeight,
      heightController,
      weightController,
    );

    _loadExistingGoalData(selectedGoalTypes, selectedTargetWeight, selectedActivityLevel, targetWeightController);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      backgroundColor: Colors.white,
      insetPadding: EdgeInsets.zero,
      child: Container(
        width: Get.width * 0.9,
        height: Get.height * 0.85,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Colors.white, Colors.white70],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Stack(
          children: [
            // 关闭按钮
            Positioned(
              top: 16,
              right: 16,
              child: GestureDetector(
                onTap: () => Get.back(result: false),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(color: Colors.white.withValues(alpha: 0.8), shape: BoxShape.circle),
                  child: const Icon(Icons.close, size: 20, color: Colors.grey),
                ),
              ),
            ),
            // 主要内容
            Column(
              children: [
                // 固定的标题和步骤指示器部分
                Container(
                  padding: const EdgeInsets.fromLTRB(20, 28, 20, 16),
                  child: Column(
                    children: [
                      const Text(
                        '欢迎加入KcalFit',
                        style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black87),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '请完成以下设置，我们将为您提供个性化的健康建议',
                        style: TextStyle(fontSize: 12, color: Colors.black54),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      // 步骤指示器
                      Obx(() => _buildStepIndicator(currentStep.value)),
                      const SizedBox(height: 16),
                      // 步骤标题和副标题
                      Obx(() => _buildStepTitle(currentStep.value)),
                    ],
                  ),
                ),

                // 可滚动的表单内容部分
                Expanded(
                  child: Obx(
                    () => SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child:
                          currentStep.value == 1
                              ? _buildStep1Content(
                                selectedGender,
                                selectedBirthDate,
                                selectedHeight,
                                selectedWeight,
                                heightController,
                                weightController,
                              )
                              : _buildStep2Content(
                                selectedGoalTypes,
                                selectedTargetWeight,
                                selectedActivityLevel,
                                targetWeightController,
                              ),
                    ),
                  ),
                ),

                // 固定的按钮部分
                Container(
                  padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
                  child: Obx(
                    () => Row(
                      children: [
                        // 上一步按钮
                        if (currentStep.value > 1)
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () => currentStep.value = currentStep.value - 1,
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
                                side: BorderSide(color: Colors.blue.shade600),
                              ),
                              child: Text('上一步', style: TextStyle(fontSize: 16, color: Colors.blue.shade600)),
                            ),
                          ),
                        if (currentStep.value > 1) const SizedBox(width: 12),
                        // 下一步/完成按钮
                        Expanded(
                          flex: currentStep.value == 1 ? 1 : 1,
                          child: ElevatedButton(
                            onPressed: () {
                              if (currentStep.value == 1) {
                                // 第一步：验证并进入下一步
                                currentStep.value = 2;
                              } else {
                                // 第二步：保存所有数据并完成
                                _saveAllUserData(
                                  selectedGender.value,
                                  selectedBirthDate.value,
                                  selectedHeight.value,
                                  selectedWeight.value,
                                  selectedGoalTypes.value,
                                  selectedTargetWeight.value,
                                  selectedActivityLevel.value,
                                );
                                Get.back(result: true);
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF4A90E2),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
                              elevation: 2,
                            ),
                            child: Text(
                              currentStep.value == 1 ? '下一步' : '完成',
                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建步骤指示器
  Widget _buildStepIndicator(int currentStep) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 第一步
        _buildStepItem(1, '身体数据', currentStep >= 1),
        // 连接线
        Container(width: 40, height: 2, color: currentStep >= 2 ? Colors.blue.shade600 : Colors.grey.shade300),
        // 第二步
        _buildStepItem(2, '健康目标', currentStep >= 2),
      ],
    );
  }

  /// 构建单个步骤项
  Widget _buildStepItem(int stepNumber, String title, bool isActive) {
    return Column(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? Colors.blue.shade600 : Colors.grey.shade300,
          ),
          child: Center(
            child: Text(
              stepNumber.toString(),
              style: TextStyle(
                color: isActive ? Colors.white : Colors.grey.shade600,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: isActive ? Colors.blue.shade600 : Colors.grey.shade600,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  /// 构建步骤标题和副标题
  Widget _buildStepTitle(int currentStep) {
    if (currentStep == 1) {
      return const Column(
        children: [
          Text('第一步：基本信息', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: Colors.black87)),
          SizedBox(height: 4),
          Text('请填写您的基本身体信息', style: TextStyle(fontSize: 14, color: Colors.black54), textAlign: TextAlign.center),
        ],
      );
    } else {
      return const Column(
        children: [
          Text('第二步：健康目标', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: Colors.black87)),
          SizedBox(height: 4),
          Text('请设定您的健康目标和期望', style: TextStyle(fontSize: 14, color: Colors.black54), textAlign: TextAlign.center),
        ],
      );
    }
  }

  /// 构建第一步内容（身体数据）
  Widget _buildStep1Content(
    RxString selectedGender,
    Rxn<DateTime> selectedBirthDate,
    RxInt selectedHeight,
    RxDouble selectedWeight,
    RulerPickerController heightController,
    RulerPickerController weightController,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UserDataInputWidgets.buildGenderSelection(selectedGender),
        const SizedBox(height: 16),
        UserDataInputWidgets.buildAgeSelection(selectedBirthDate),
        const SizedBox(height: 16),
        UserDataInputWidgets.buildHeightSelection(selectedHeight, heightController),
        const SizedBox(height: 16),
        UserDataInputWidgets.buildWeightSelection(selectedWeight, weightController),
        const SizedBox(height: 20),
      ],
    );
  }

  /// 构建第二步内容（目标设置）
  Widget _buildStep2Content(
    RxString selectedGoalTypes,
    RxDouble selectedTargetWeight,
    RxString selectedActivityLevel,
    RulerPickerController targetWeightController,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildGoalTypeSelection(selectedGoalTypes),
        const SizedBox(height: 16),
        _buildTargetWeightSelection(selectedTargetWeight, targetWeightController),
        const SizedBox(height: 16),
        _buildActivityLevelSelection(selectedActivityLevel),
        const SizedBox(height: 20),
      ],
    );
  }

  /// 保存所有用户数据
  Future<void> _saveAllUserData(
    String gender,
    DateTime? birthDate,
    int height,
    double weight,
    String goalTypes,
    double targetWeight,
    String activityLevel,
  ) async {
    try {
      // 保存身体数据
      if (birthDate != null) {
        await _saveUserSelections(gender, birthDate, height, weight);
      }

      // 保存目标设置
      await _saveGoalSelections(goalTypes, targetWeight, activityLevel);

      LoggerUtil.i('所有用户数据保存完成');
    } catch (e) {
      LoggerUtil.e('保存所有用户数据失败', e);
    }
  }

  /// 加载用户已有数据进行预填充
  Future<void> _loadExistingUserData(
    RxString selectedGender,
    Rxn<DateTime> selectedBirthDate,
    RxInt selectedHeight,
    RxDouble selectedWeight,
    RulerPickerController heightController,
    RulerPickerController weightController,
  ) async {
    try {
      // 从body_data表中读取最新的用户数据
      final latestBodyData = await _bodyDataDao.getLatestBodyData();
      if (latestBodyData != null) {
        // 读取性别
        if (latestBodyData.gender != null && (latestBodyData.gender == '男' || latestBodyData.gender == '女')) {
          selectedGender.value = latestBodyData.gender!;
          LoggerUtil.i('预填充性别: ${latestBodyData.gender}');
        }

        // 读取出生日期
        if (latestBodyData.birthDate != null) {
          selectedBirthDate.value = latestBodyData.birthDate!;
          LoggerUtil.i('预填充出生日期: ${latestBodyData.birthDate}');
        }
        // 如果没有出生日期数据，保持默认值不变

        // 读取身高
        if (latestBodyData.height != null && latestBodyData.height! >= 140 && latestBodyData.height! <= 200) {
          selectedHeight.value = latestBodyData.height!;
          heightController.value = selectedHeight.value.toDouble();
          LoggerUtil.i('预填充身高: ${latestBodyData.height}cm');
        }

        // 读取体重
        if (latestBodyData.weight != null && latestBodyData.weight! >= 30 && latestBodyData.weight! <= 150) {
          selectedWeight.value = latestBodyData.weight!; // 直接使用double值
          weightController.value = selectedWeight.value;
          LoggerUtil.i('预填充体重: ${selectedWeight.value}kg');
        }
      }
    } catch (e) {
      LoggerUtil.e('加载用户已有数据失败', e);
    }
  }

  /// 加载用户已有目标数据进行预填充
  Future<void> _loadExistingGoalData(
    RxString selectedGoalTypes,
    RxDouble selectedTargetWeight,
    RxString selectedActivityLevel,
    RulerPickerController targetWeightController,
  ) async {
    try {
      // 从Hive加载已有的目标数据
      final goalTypes = await HiveService.getData<String>("goals", "goalTypes");
      final targetWeight = await HiveService.getData("goals", "targetWeight");
      final activityLevel = await HiveService.getData<String>("goals", "activityLevel");

      if (goalTypes != null && goalTypes.isNotEmpty) {
        selectedGoalTypes.value = goalTypes;
        LoggerUtil.i('预填充目标类型: $goalTypes');
      }

      if (targetWeight != null) {
        final weight = double.tryParse(targetWeight.toString()) ?? 60.0;
        if (weight >= 30 && weight <= 150) {
          selectedTargetWeight.value = weight;
          targetWeightController.value = weight;
          LoggerUtil.i('预填充目标体重: ${weight}kg');
        }
      }

      if (activityLevel != null && activityLevel.isNotEmpty) {
        selectedActivityLevel.value = activityLevel;
        LoggerUtil.i('预填充活动水平: $activityLevel');
      }
    } catch (e) {
      LoggerUtil.e('加载用户已有目标数据失败', e);
    }
  }

  /// 保存用户选择的数据
  Future<void> _saveUserSelections(String gender, DateTime birthDate, int height, double weight) async {
    try {
      // 检查是否已有身体数据记录
      final latestBodyData = await _bodyDataDao.getLatestBodyData();

      if (latestBodyData != null) {
        // 更新现有记录
        final updatedBodyData = latestBodyData.copyWith(
          gender: gender,
          birthDate: birthDate,
          height: height,
          weight: weight, // 直接保存体重值，支持小数
        );
        await _bodyDataDao.update(updatedBodyData);
        LoggerUtil.i('更新身体数据记录: 性别=$gender, 出生日期=$birthDate, 身高=${height}cm, 体重=${weight}kg');
      } else {
        // 创建新的身体数据记录
        final newBodyData = BodyData(
          recordDate: DateTime.now(),
          gender: gender,
          birthDate: birthDate,
          height: height,
          weight: weight, // 直接保存体重值，支持小数
        );
        await _bodyDataDao.insert(newBodyData);
        LoggerUtil.i('创建新身体数据记录: 性别=$gender, 出生日期=$birthDate, 身高=${height}cm, 体重=${weight}kg');
      }
    } catch (e) {
      LoggerUtil.e('保存用户选择失败', e);
    }
  }

  /// 保存目标设置选择的数据
  Future<void> _saveGoalSelections(String goalTypes, double targetWeight, String activityLevel) async {
    try {
      // 保存到Hive
      await HiveService.saveData("goals", "goalTypes", goalTypes);
      await HiveService.saveData("goals", "targetWeight", targetWeight);
      await HiveService.saveData("goals", "activityLevel", activityLevel);
      await HiveService.saveData("goals", "createdTime", DateTime.now().toIso8601String());

      LoggerUtil.i('保存目标设置: 目标类型=$goalTypes, 目标体重=${targetWeight}kg, 活动水平=$activityLevel');
    } catch (e) {
      LoggerUtil.e('保存目标设置失败', e);
    }
  }

  /// 标记首次启动完成
  Future<void> _markFirstLaunchComplete() async {
    try {
      await HiveService.saveData(_boxName, _firstLaunchKey, false);
      isFirstLaunch.value = false;
      LoggerUtil.i('首次启动标记已完成');
    } catch (e) {
      LoggerUtil.e('标记首次启动完成失败', e);
    }
  }

  /// 标记身体数据引导已显示
  Future<void> _markBodyDataGuideShown() async {
    try {
      await HiveService.saveData(_boxName, _bodyDataGuideShownKey, true);
      bodyDataGuideShown.value = true;
      LoggerUtil.i('身体数据引导已标记为显示');
    } catch (e) {
      LoggerUtil.e('标记身体数据引导已显示失败', e);
    }
  }

  /// 从特定页面触发身体数据引导
  /// 无身体数据时直接显示引导
  /// [fromPage] 触发引导的页面名称，用于日志记录
  Future<void> checkAndShowBodyDataGuideFromPage(String fromPage) async {
    try {
      // 如果正在显示引导，则不重复显示
      if (_isShowingGuide.value) {
        LoggerUtil.i('用户引导正在显示中，跳过来自$fromPage的重复调用');
        return;
      }

      // 复用现有的检查逻辑
      if (await _shouldShowBodyDataGuide()) {
        LoggerUtil.i('从$fromPage页面触发身体数据引导');
        await _showBodyDataGuide();
      } else {
        LoggerUtil.i('从$fromPage页面检查，已有身体数据，不显示引导');
      }
    } catch (e) {
      LoggerUtil.e('从$fromPage页面检查用户引导失败', e);
    }
  }

  /// 判断是否应该显示目标设置引导
  /// 检查用户是否已设置目标
  Future<bool> _shouldShowGoalSettingGuide() async {
    try {
      // 检查是否已有目标设置数据
      final goalTypes = await HiveService.getData<String>("goals", "goalTypes");
      final targetWeight = await HiveService.getData("goals", "targetWeight");
      final activityLevel = await HiveService.getData<String>("goals", "activityLevel");

      final hasGoalData =
          (goalTypes != null && goalTypes.isNotEmpty) ||
          (targetWeight != null) ||
          (activityLevel != null && activityLevel.isNotEmpty);

      LoggerUtil.i(hasGoalData ? '已有目标设置，不显示引导' : '无目标设置，需要显示引导');
      return !hasGoalData;
    } catch (e) {
      LoggerUtil.e('检查目标设置引导条件失败', e);
      return false;
    }
  }

  /// 显示目标设置引导（现在使用统一的引导对话框）
  Future<void> _showGoalSettingGuide() async {
    try {
      LoggerUtil.i('显示目标设置引导（统一引导）');

      // 设置正在显示引导标志
      _isShowingGuide.value = true;

      // 显示统一的引导对话框
      final dialogResult = await Get.dialog<bool>(_buildUnifiedGuideDialog(), barrierDismissible: false);

      // 标记引导已显示
      await _markBodyDataGuideShown();
      await _markGoalSettingGuideShown();

      LoggerUtil.i('目标设置引导完成，用户选择: ${dialogResult == true ? "确定" : "关闭"}');
    } catch (e) {
      LoggerUtil.e('显示目标设置引导失败', e);
    } finally {
      // 重置正在显示引导标志
      _isShowingGuide.value = false;
    }
  }

  /// 标记目标设置引导已显示
  Future<void> _markGoalSettingGuideShown() async {
    try {
      await HiveService.saveData(_boxName, _goalSettingGuideShownKey, true);
      goalSettingGuideShown.value = true;
      LoggerUtil.i('目标设置引导已标记为显示');
    } catch (e) {
      LoggerUtil.e('标记目标设置引导已显示失败', e);
    }
  }

  /// 构建目标类型选择组件
  Widget _buildGoalTypeSelection(RxString selectedGoalTypes) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.flag, color: Colors.blue.shade600, size: 20),
            const SizedBox(width: 8),
            const Text('健康目标', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
          ],
        ),
        const SizedBox(height: 8),
        Obx(
          () => Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                HealthGoals.options.map((goal) {
                  final isSelected = selectedGoalTypes.value.split(',').contains(goal);
                  return GestureDetector(
                    onTap: () {
                      List<String> currentGoals =
                          selectedGoalTypes.value.isNotEmpty ? selectedGoalTypes.value.split(',') : [];
                      if (currentGoals.contains(goal)) {
                        currentGoals.remove(goal);
                      } else {
                        currentGoals.add(goal);
                      }
                      selectedGoalTypes.value = currentGoals.join(',');
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.blue.shade600 : Colors.grey[100],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300),
                      ),
                      child: Text(
                        goal,
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.grey[700],
                          fontSize: 12,
                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }

  /// 构建目标体重选择组件
  Widget _buildTargetWeightSelection(RxDouble selectedTargetWeight, RulerPickerController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.scale, color: Colors.blue.shade600, size: 20),
            const SizedBox(width: 8),
            const Text('目标体重', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Obx(
            () => Text(
              '${selectedTargetWeight.value.toStringAsFixed(1)} kg',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18, color: Colors.blue.shade800, fontWeight: FontWeight.w600),
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 60,
          child: RulerPicker(
            controller: controller,
            onBuildRulerScaleText: (index, value) {
              // 每1kg显示一次数值
              if (value % 1 == 0) {
                return value.toInt().toString();
              }
              return '';
            },
            ranges: const [
              RulerRange(begin: 30, end: 150, scale: 0.1), // 体重范围30-150kg，刻度为0.1kg
            ],
            scaleLineStyleList: const [
              ScaleLineStyle(color: Colors.grey, width: 1.5, height: 30, scale: 0),
              ScaleLineStyle(color: Colors.grey, width: 1, height: 25, scale: 10),
              ScaleLineStyle(color: Colors.grey, width: 0.5, height: 15, scale: -1),
            ],
            onValueChanged: (value) {
              selectedTargetWeight.value = double.parse(value.toStringAsFixed(1));
            },
            width: Get.width * 0.7,
            height: 60,
            rulerMarginTop: 8,
            marker: Container(
              width: 2,
              height: 40,
              decoration: BoxDecoration(color: Colors.blue.shade600, borderRadius: BorderRadius.circular(1)),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建活动水平选择组件
  Widget _buildActivityLevelSelection(RxString selectedActivityLevel) {
    final activityLevels = ['久坐不动', '轻度活动', '中度活动', '高强度活动'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.directions_run, color: Colors.blue.shade600, size: 20),
            const SizedBox(width: 8),
            const Text('活动水平', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
          ],
        ),
        const SizedBox(height: 8),
        Obx(
          () => Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                activityLevels.map((level) {
                  final isSelected = selectedActivityLevel.value == level;
                  return GestureDetector(
                    onTap: () => selectedActivityLevel.value = level,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.blue.shade600 : Colors.grey[100],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300),
                      ),
                      child: Text(
                        level,
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.grey[700],
                          fontSize: 12,
                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }

  /// 重置引导状态（用于测试或重置功能）
  Future<void> resetGuideStatus() async {
    try {
      await HiveService.saveData(_boxName, _firstLaunchKey, true);
      await HiveService.saveData(_boxName, _bodyDataGuideShownKey, false);
      await HiveService.saveData(_boxName, _goalSettingGuideShownKey, false);
      isFirstLaunch.value = true;
      bodyDataGuideShown.value = false;
      goalSettingGuideShown.value = false;
      // 重置显示标志
      _isShowingGuide.value = false;
      LoggerUtil.i('用户引导状态已重置');
    } catch (e) {
      LoggerUtil.e('重置用户引导状态失败', e);
    }
  }
}
