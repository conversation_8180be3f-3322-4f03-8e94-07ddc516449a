import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kcal_fit/app/modules/my/controllers/my_controller.dart';

import '../../../../core/utils/logger_util.dart';
import '../../../data/services/hive/hive_service.dart';
import '../../../data/services/sqflite/dao/impl/body_data_dao.dart';
import '../../../data/models/body_data.dart';

/// 个人信息控制器
/// 负责管理个人信息的状态和业务逻辑
class PersonalInfoController extends GetxController {
  // 数据访问对象
  final BodyDataDao _bodyDataDao = BodyDataDao();

  // 表单相关
  final formKey = GlobalKey<FormState>();
  final nicknameController = TextEditingController();

  // 验证错误信息
  final RxString nicknameError = ''.obs;
  final RxString genderError = ''.obs;
  final RxString birthDateError = ''.obs;

  // 加载状态
  final RxBool isLoading = false.obs;
  final RxBool isSaving = false.obs;

  // 个人信息数据
  var nickname = ''.obs;
  var gender = ''.obs;
  final Rxn<DateTime> birthDate = Rxn<DateTime>(DateTime.parse('1990-01-01')); // 使用final和明确类型声明
  var userId = ''.obs;

  @override
  void onInit() {
    _initializeData();
    super.onInit();
  }

  @override
  void onClose() {
    nicknameController.dispose();
    super.onClose();
  }

  /// 初始化数据
  /// 从Hive和body_data表中加载数据到可观察变量
  Future<void> _initializeData() async {
    try {
      LoggerUtil.i('🔄 开始加载个人信息数据...');

      // 从Hive读取昵称
      final storedNickname = await HiveService.getData("user", "nickname");
      if (storedNickname != null) {
        nickname.value = storedNickname;
        nicknameController.text = storedNickname;
        LoggerUtil.i('✅ 昵称加载成功: $storedNickname');
      } else {
        LoggerUtil.w('⚠️ 未找到存储的昵称');
      }

      // 从body_data表中读取性别和出生日期
      final latestBodyData = await _bodyDataDao.getLatestBodyData();
      if (latestBodyData != null) {
        if (latestBodyData.gender != null) {
          gender.value = latestBodyData.gender!;
          LoggerUtil.i('✅ 性别加载成功: ${latestBodyData.gender}');
        }
        if (latestBodyData.birthDate != null) {
          birthDate.value = latestBodyData.birthDate!;
          LoggerUtil.i('✅ 出生日期加载成功: ${latestBodyData.birthDate}');
        }
        LoggerUtil.i('✅ 身体数据加载完成');
      } else {
        LoggerUtil.w('⚠️ 未找到身体数据记录');
      }

      LoggerUtil.i('🎉 个人信息数据加载完成');
    } catch (e) {
      LoggerUtil.e('❌ 个人信息数据加载失败', e);
      // 显示错误提示，但不阻止页面显示
      Get.snackbar(
        '提示',
        '加载个人信息时出现问题，请检查数据',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.orange.shade100,
        colorText: Colors.orange.shade800,
      );
    }
  }

  /// 验证昵称
  bool _validateNickname() {
    final currentNickname = nicknameController.text.trim();
    if (currentNickname.isEmpty) {
      nicknameError.value = '请输入昵称';
      return false;
    }
    if (currentNickname.length < 2) {
      nicknameError.value = '昵称至少需要2个字符';
      return false;
    }
    if (currentNickname.length > 20) {
      nicknameError.value = '昵称不能超过20个字符';
      return false;
    }
    nicknameError.value = '';
    return true;
  }

  /// 验证性别
  bool _validateGender() {
    if (gender.value.isEmpty) {
      genderError.value = '请选择性别';
      return false;
    }
    genderError.value = '';
    return true;
  }

  /// 验证出生日期
  bool _validateBirthDate() {
    final currentBirthDate = birthDate.value;
    if (currentBirthDate == null) {
      birthDateError.value = '请选择出生日期';
      return false;
    }

    final now = DateTime.now();
    final age = now.year - currentBirthDate.year;

    if (age < 1) {
      birthDateError.value = '年龄不能小于1岁';
      return false;
    }
    if (age > 150) {
      birthDateError.value = '年龄不能大于150岁';
      return false;
    }

    birthDateError.value = '';
    return true;
  }

  /// 验证所有表单字段
  bool _validateForm() {
    bool isValid = true;

    if (!_validateNickname()) isValid = false;
    if (!_validateGender()) isValid = false;
    if (!_validateBirthDate()) isValid = false;

    return isValid;
  }

  /// 更新昵称
  void updateNickname(String value) {
    nickname.value = value.trim();
    // 实时验证
    _validateNickname();
  }

  /// 更新性别
  void updateGender(String newGender) {
    gender.value = newGender;
    _validateGender();
  }

  /// 更新出生日期
  void updateBirthDate(DateTime date) {
    birthDate.value = date;
    _validateBirthDate();
  }

  /// 加载用户个人信息
  /// [uid] 用户ID
  Future<void> loadPersonalInfo(String uid) async {
    try {
      isLoading.value = true;
      userId.value = uid;

      // 复用初始化数据的逻辑
      await _initializeData();
    } catch (e) {
      Get.snackbar(
        '错误',
        '加载个人信息失败: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 保存个人信息到数据库
  Future<bool> savePersonalInfo() async {
    try {
      // 验证表单
      if (!_validateForm()) {
        Get.snackbar(
          '验证失败',
          '请检查并完善所有必填信息',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.orange.shade100,
          colorText: Colors.orange.shade800,
        );
        return false;
      }

      isSaving.value = true;

      // 保存昵称到Hive
      await HiveService.saveData("user", "nickname", nickname.value);

      // 保存性别和出生日期到body_data表
      await _saveBodyDataInfo();

      // 重置表单
      resetForm();
      //返回上一页
      Get.find<MyController>().loadBodyData();
      Get.back(result: {'action': 'personal_info_saved', 'needRefresh': true});
      Get.snackbar(
        '成功',
        '个人信息已保存',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );

      return true;
    } catch (e) {
      Get.snackbar(
        '错误',
        '保存个人信息失败: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return false;
    } finally {
      isSaving.value = false;
    }
  }

  /// 保存身体数据信息到数据库
  Future<void> _saveBodyDataInfo() async {
    final latestBodyData = await _bodyDataDao.getLatestBodyData();
    if (latestBodyData != null) {
      // 更新现有记录
      final updatedBodyData = latestBodyData.copyWith(gender: gender.value, birthDate: birthDate.value);
      await _bodyDataDao.update(updatedBodyData);
    } else {
      // 创建新的身体数据记录
      final newBodyData = BodyData(recordDate: DateTime.now(), gender: gender.value, birthDate: birthDate.value);
      await _bodyDataDao.insert(newBodyData);
    }
  }

  /// 重置表单
  void resetForm() {
    nicknameController.clear();
    nickname.value = '';
    gender.value = '';
    birthDate.value = null; // 重置为null

    // 清除错误信息
    nicknameError.value = '';
    genderError.value = '';
    birthDateError.value = '';
  }
}
