import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_ruler_picker/flutter_ruler_picker.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../controllers/goal_setting_controller.dart';

class GoalSettingView extends GetView<GoalSettingController> {
  const GoalSettingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: SmartAppBar(
        title: '目标设定',
        actions: [
          Obx(
            () => TextButton(
              onPressed: controller.isSaving ? null : controller.saveGoalSettings,
              child:
                  controller.isSaving
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                      : const Text('保存', style: TextStyle(color: Colors.white, fontSize: 14)),
            ),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return Form(
          key: controller.formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCurrentStatusCard(),
                const SizedBox(height: 16),
                _buildGoalTypeSection(),
                const SizedBox(height: 16),
                _buildTargetWeightSection(context),
                const SizedBox(height: 16),
                _buildActivityLevelSection(),
                const SizedBox(height: 16),
                _buildTimelineSection(),
                const SizedBox(height: 16),
                _buildGoalSummaryCard(),
                const SizedBox(height: 20),
              ],
            ),
          ),
        );
      }),
    );
  }

  /// 构建当前状态卡片
  Widget _buildCurrentStatusCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person_outline, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('当前状态', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),

          Obx(
            () => Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    '当前体重',
                    '${controller.currentWeight.value.toStringAsFixed(1)}kg',
                    Icons.scale,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    '身高',
                    '${controller.currentHeight.value.toStringAsFixed(0)}cm',
                    Icons.straighten,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem('BMI', controller.currentBMI.value.toStringAsFixed(1), Icons.favorite),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态项目
  Widget _buildStatusItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.blue.shade400, size: 20),
        const SizedBox(height: 8),
        Text(value, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  /// 构建目标类型选择
  Widget _buildGoalTypeSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('目标类型', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          const SizedBox(height: 16),

          Obx(
            () => Wrap(
              spacing: 12,
              runSpacing: 12,
              children:
                  controller.goalTypes.map((type) {
                    final isSelected = controller.isGoalTypeSelected(type);
                    return GestureDetector(
                      onTap: () => controller.toggleGoalType(type),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.blue.shade600 : Colors.grey[100],
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300),
                        ),
                        child: Text(
                          type,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey[700],
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建目标体重设置
  Widget _buildTargetWeightSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.scale, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('目标体重', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),

          // 显示当前选中的目标体重值
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Obx(
              () => Text(
                '${controller.targetWeight.toStringAsFixed(1)} kg',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 24, color: Colors.blue.shade800, fontWeight: FontWeight.w600),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 目标体重选择尺子
          SizedBox(
            height: 80,
            child: RulerPicker(
              controller: controller.targetWeightRulerController,
              onBuildRulerScaleText: (index, value) {
                // 每1kg显示一次数值，让数值显示更密集
                if (value % 1 == 0) {
                  return value.toInt().toString();
                }
                return '';
              },
              ranges: const [
                RulerRange(begin: 30, end: 150, scale: 0.1), // 体重范围30-150kg，刻度为0.1kg
              ],
              scaleLineStyleList: [
                ScaleLineStyle(
                  color: Colors.blue.shade400,
                  width: 2,
                  height: 35,
                  scale: 0, // 主刻度线（每5kg）
                ),
                ScaleLineStyle(
                  color: Colors.blue.shade300,
                  width: 1.5,
                  height: 28,
                  scale: 10, // 中刻度线（每1kg）
                ),
                ScaleLineStyle(
                  color: Colors.blue.shade200,
                  width: 1,
                  height: 18,
                  scale: -1, // 小刻度线（每0.1kg）
                ),
              ],
              onValueChanged: (value) {
                controller.updateTargetWeight(value.toDouble());
              },
              width: Get.width * 0.8,
              height: 80,
              rulerMarginTop: 8,
              // 自定义标记线
              marker: Container(
                width: 3,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.blue.shade600,
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: [
                    BoxShadow(color: Colors.blue.withValues(alpha: 0.3), blurRadius: 4, offset: const Offset(0, 2)),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),

          // 提示文本
          Text('滑动选择您的目标体重', textAlign: TextAlign.center, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }

  /// 构建活动水平选择
  Widget _buildActivityLevelSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.directions_run, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('活动水平', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 20),

          Obx(
            () => Column(
              children:
                  controller.activityLevels.map((activity) {
                    final isSelected = controller.activityLevel == activity['level'];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => controller.updateActivityLevel(activity['level']!),
                          borderRadius: BorderRadius.circular(16),
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              gradient:
                                  isSelected
                                      ? LinearGradient(
                                        colors: [Colors.blue.shade50, Colors.blue.shade100],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      )
                                      : null,
                              color: isSelected ? null : Colors.grey[50],
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: isSelected ? Colors.blue.shade400 : Colors.grey.shade200,
                                width: isSelected ? 2 : 1,
                              ),
                              boxShadow:
                                  isSelected
                                      ? [
                                        BoxShadow(
                                          color: Colors.blue.withValues(alpha: 0.2),
                                          blurRadius: 8,
                                          offset: const Offset(0, 4),
                                        ),
                                      ]
                                      : null,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: isSelected ? Colors.blue.shade600 : Colors.transparent,
                                    border: Border.all(
                                      color: isSelected ? Colors.blue.shade600 : Colors.grey.shade400,
                                      width: 2,
                                    ),
                                  ),
                                  child: isSelected ? Icon(Icons.check, size: 16, color: Colors.white) : null,
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        activity['level']!,
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color: isSelected ? Colors.blue.shade800 : Colors.grey[800],
                                        ),
                                      ),
                                      const SizedBox(height: 6),
                                      Text(
                                        activity['description']!,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: isSelected ? Colors.blue.shade600 : Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (isSelected)
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.shade600,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Text(
                                      '已选择',
                                      style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.w500),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建时间线设置
  Widget _buildTimelineSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('目标期限', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          const SizedBox(height: 16),

          Obx(
            () => Wrap(
              spacing: 12,
              runSpacing: 12,
              children:
                  controller.targetDaysOptions.map((days) {
                    final isSelected = controller.targetDays == days;
                    return GestureDetector(
                      onTap: () => controller.updateTargetDays(days),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.blue.shade600 : Colors.grey[100],
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300),
                        ),
                        child: Text(
                          // ignore: unnecessary_brace_in_string_interps
                          '${days}天',
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey[700],
                            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建目标摘要卡片
  Widget _buildGoalSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.blue.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flag, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              Text('目标摘要', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: Colors.blue.shade800)),
            ],
          ),
          const SizedBox(height: 16),

          Obx(
            () => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  controller.getGoalDescription(),
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.blue.shade700),
                ),
                const SizedBox(height: 8),
                Text(
                  '建议变化速度: ${controller.getWeeklyWeightChange()}',
                  style: TextStyle(fontSize: 14, color: Colors.blue.shade600),
                ),
                const SizedBox(height: 8),
                Text('活动水平: ${controller.activityLevel}', style: TextStyle(fontSize: 14, color: Colors.blue.shade600)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
